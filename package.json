{"name": "erp-ts", "version": "1.0.0", "description": "TypeScript ERP system with decorator support, PostgreSQL, and comprehensive tooling", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "dev": "nodemon --exec ts-node src/server.ts", "dev:lib": "nodemon --exec ts-node src/index.ts", "start": "node dist/server.js", "start:lib": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src tests --ext .ts", "lint:fix": "eslint src tests --ext .ts --fix", "format": "prettier --write \"src/**/*.ts\" \"tests/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"tests/**/*.ts\"", "typecheck": "tsc --noEmit", "clean": "rimraf dist coverage", "prepare": "husky"}, "keywords": ["typescript", "decorators", "postgresql", "erp", "nodejs"], "author": "", "license": "ISC", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "lint-staged": {"*.ts": ["eslint --fix", "prettier --write", "git add"], "*.{json,md}": ["prettier --write", "git add"]}, "devDependencies": {"@jest/globals": "^30.0.4", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.13", "@types/pg": "^8.15.4", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "dotenv": "^17.2.0", "eslint": "^9.31.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "husky": "^9.1.7", "jest": "^30.0.4", "lint-staged": "^16.1.2", "nodemon": "^3.1.10", "prettier": "^3.6.2", "rimraf": "^6.0.1", "supertest": "^7.1.3", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "dependencies": {"compression": "^1.8.0", "cors": "^2.8.5", "express": "^5.1.0", "helmet": "^8.1.0", "pg": "^8.16.3", "reflect-metadata": "^0.2.2", "uuid": "^11.1.0"}}