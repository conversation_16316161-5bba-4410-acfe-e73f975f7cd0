/**
 * JSON RPC 2.0 types and interfaces
 */

export interface JsonRpcRequest {
  jsonrpc: '2.0';
  method: string;
  params?: any[] | Record<string, any>;
  id?: string | number | null;
}

export interface JsonRpcResponse {
  jsonrpc: '2.0';
  result?: any;
  error?: JsonRpcError;
  id: string | number | null;
}

export interface JsonRpcError {
  code: number;
  message: string;
  data?: any;
}

export interface JsonRpcNotification {
  jsonrpc: '2.0';
  method: string;
  params?: any[] | Record<string, any>;
}

// Standard JSON RPC error codes
export enum JsonRpcErrorCode {
  PARSE_ERROR = -32700,
  INVALID_REQUEST = -32600,
  METHOD_NOT_FOUND = -32601,
  INVALID_PARAMS = -32602,
  INTERNAL_ERROR = -32603,
  // Custom application errors: -32000 to -32099
  AUTHENTICATION_ERROR = -32001,
  AUTHORIZATION_ERROR = -32002,
  VALIDATION_ERROR = -32003,
  BUSINESS_LOGIC_ERROR = -32004,
  DATABASE_ERROR = -32005,
}

export interface RpcMethodHandler {
  (params: any, context: RpcContext): Promise<any>;
}

export interface RpcContext {
  requestId: string;
  correlationId: string;
  userId?: string;
  sessionId?: string;
  metadata: Record<string, any>;
}

export interface RpcMethodDefinition {
  name: string;
  handler: RpcMethodHandler;
  description?: string;
  params?: {
    type: 'array' | 'object';
    schema?: any; // JSON Schema
  };
  returns?: {
    schema?: any; // JSON Schema
  };
  auth?: boolean;
  permissions?: string[];
}

export interface RpcMiddleware {
  (request: JsonRpcRequest, context: RpcContext, next: () => Promise<any>): Promise<any>;
}

export interface RpcServerOptions {
  cors?: boolean;
  maxRequestSize?: string;
  timeout?: number;
  enableIntrospection?: boolean;
  enableMetrics?: boolean;
}

export interface RpcMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  methodStats: Record<string, {
    calls: number;
    errors: number;
    totalTime: number;
    averageTime: number;
  }>;
}

export interface RpcMetrics {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  methodStats: Record<string, {
    calls: number;
    errors: number;
    totalTime: number;
    averageTime: number;
  }>;
}
