/**
 * System-level RPC methods
 */

import { RpcMethodDefinition } from '../types';

export const systemMethods: RpcMethodDefinition[] = [
  {
    name: 'system.ping',
    handler: async () => {
      return {
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      };
    },
    description: 'Health check endpoint that returns system status',
  },

  {
    name: 'system.version',
    handler: async () => {
      return {
        version: '1.0.0',
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
      };
    },
    description: 'Get system version information',
  },

  {
    name: 'system.stats',
    handler: async () => {
      const memUsage = process.memoryUsage();
      return {
        memory: {
          rss: memUsage.rss,
          heapTotal: memUsage.heapTotal,
          heapUsed: memUsage.heapUsed,
          external: memUsage.external,
        },
        uptime: process.uptime(),
        pid: process.pid,
        cpuUsage: process.cpuUsage(),
      };
    },
    description: 'Get detailed system statistics',
    auth: true, // Requires authentication
  },

  {
    name: 'system.time',
    handler: async (params) => {
      const timezone = params?.timezone || 'UTC';
      const now = new Date();
      
      return {
        timestamp: now.getTime(),
        iso: now.toISOString(),
        timezone,
        formatted: now.toLocaleString('en-US', { timeZone: timezone }),
      };
    },
    description: 'Get current server time with optional timezone',
    params: {
      type: 'object',
      schema: {
        type: 'object',
        properties: {
          timezone: {
            type: 'string',
            description: 'IANA timezone identifier (e.g., America/New_York)',
          },
        },
      },
    },
  },
];
