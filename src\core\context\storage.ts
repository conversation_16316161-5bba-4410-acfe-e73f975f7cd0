/**
 * Async Local Storage implementation for request context
 */

import { AsyncLocalStorage } from 'async_hooks';
import { v4 as uuidv4 } from 'uuid';
import { ContextStore, RequestContext } from './types';

export class ContextStorage {
  private static instance: ContextStorage;
  private storage: AsyncLocalStorage<ContextStore>;

  private constructor() {
    this.storage = new AsyncLocalStorage<ContextStore>();
  }

  static getInstance(): ContextStorage {
    if (!ContextStorage.instance) {
      ContextStorage.instance = new ContextStorage();
    }
    return ContextStorage.instance;
  }

  run<T>(context: ContextStore, callback: () => T): T {
    return this.storage.run(context, callback);
  }

  getStore(): ContextStore | undefined {
    return this.storage.getStore();
  }

  getContext(): RequestContext | undefined {
    const store = this.getStore();
    return store?.request;
  }

  setContext(context: Partial<RequestContext>): void {
    const store = this.getStore();
    if (store) {
      store.request = {
        requestId: uuidv4(),
        correlationId: uuidv4(),
        startTime: new Date(),
        metadata: {},
        ...context,
      };
    }
  }

  updateContext(updates: Partial<RequestContext>): void {
    const store = this.getStore();
    if (store?.request) {
      Object.assign(store.request, updates);
    }
  }

  createRequestContext(overrides: Partial<RequestContext> = {}): RequestContext {
    return {
      requestId: uuidv4(),
      correlationId: uuidv4(),
      startTime: new Date(),
      metadata: {},
      ...overrides,
    };
  }

  // Utility methods for common context operations
  getRequestId(): string | undefined {
    return this.getContext()?.requestId;
  }

  getCorrelationId(): string | undefined {
    return this.getContext()?.correlationId;
  }

  getUserId(): string | undefined {
    return this.getContext()?.userId;
  }

  getSessionId(): string | undefined {
    return this.getContext()?.sessionId;
  }

  setUserId(userId: string): void {
    this.updateContext({ userId });
  }

  setSessionId(sessionId: string): void {
    this.updateContext({ sessionId });
  }

  setMetadata(key: string, value: any): void {
    const context = this.getContext();
    if (context) {
      context.metadata[key] = value;
    }
  }

  getMetadata(key: string): any {
    return this.getContext()?.metadata[key];
  }

  getAllMetadata(): Record<string, any> {
    return this.getContext()?.metadata || {};
  }

  // Performance tracking
  getRequestDuration(): number {
    const context = this.getContext();
    if (context) {
      return Date.now() - context.startTime.getTime();
    }
    return 0;
  }

  // Logging helpers
  getLogContext(): Record<string, any> {
    const context = this.getContext();
    if (!context) {
      return {};
    }

    return {
      requestId: context.requestId,
      correlationId: context.correlationId,
      userId: context.userId,
      sessionId: context.sessionId,
      duration: this.getRequestDuration(),
    };
  }
}

// Export singleton instance
export const contextStorage = ContextStorage.getInstance();
