/**
 * RPC Server Setup and Configuration
 */

import { JsonRpcServer } from './server';
import { systemMethods, userMethods, exampleMethods } from './methods';
import { RpcMiddleware } from './types';

/**
 * Create and configure a JSON RPC server with all methods and middleware
 */
export function createRpcServer(): JsonRpcServer {
  const server = new JsonRpcServer({
    cors: true,
    maxRequestSize: '10mb',
    timeout: 30000,
    enableIntrospection: true,
    enableMetrics: true,
  });

  // Add logging middleware
  const loggingMiddleware: RpcMiddleware = async (request, context, next) => {
    const startTime = Date.now();
    console.log(`🔄 RPC Call: ${request.method}`, {
      requestId: context.requestId,
      correlationId: context.correlationId,
      userId: context.userId,
      params: request.params,
    });

    try {
      const result = await next();
      const duration = Date.now() - startTime;
      console.log(`✅ RPC Success: ${request.method} (${duration}ms)`, {
        requestId: context.requestId,
      });
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      console.error(`❌ RPC Error: ${request.method} (${duration}ms)`, {
        requestId: context.requestId,
        error: error.message,
      });
      throw error;
    }
  };

  // Add rate limiting middleware (basic implementation)
  const rateLimitMiddleware: RpcMiddleware = async (request, context, next) => {
    // In a real implementation, you would use Redis or similar for distributed rate limiting
    // This is a simple in-memory example
    const key = `${context.userId || context.correlationId}:${request.method}`;
    
    // For demo purposes, just log the rate limit check
    console.log(`🚦 Rate limit check for ${key}`);
    
    return next();
  };

  // Add authentication middleware
  const authMiddleware: RpcMiddleware = async (request, context, next) => {
    // Skip auth for introspection and system methods
    if (request.method.startsWith('rpc.') || request.method.startsWith('system.')) {
      return next();
    }

    // For demo purposes, accept any user ID from headers
    // In production, you would validate JWT tokens, etc.
    if (!context.userId && request.method !== 'example.echo') {
      console.log(`🔐 Auth required for ${request.method}, but no user ID provided`);
    }

    return next();
  };

  // Register middleware in order
  server.use(loggingMiddleware);
  server.use(rateLimitMiddleware);
  server.use(authMiddleware);

  // Register all method groups
  systemMethods.forEach(method => server.register(method));
  userMethods.forEach(method => server.register(method));
  exampleMethods.forEach(method => server.register(method));

  console.log('🚀 JSON RPC Server configured with methods:', server.listMethods());

  return server;
}

/**
 * Setup RPC server for Express application
 */
export function setupRpcEndpoint(app: any, path: string = '/rpc'): JsonRpcServer {
  const rpcServer = createRpcServer();
  
  // Add JSON body parser middleware for the RPC endpoint
  app.use(path, (req: any, res: any, next: any) => {
    // Ensure we have JSON body parsing
    if (!req.body) {
      return res.status(400).json({
        jsonrpc: '2.0',
        error: {
          code: -32700,
          message: 'Parse error - request body is required',
        },
        id: null,
      });
    }
    next();
  });

  // Mount the RPC server middleware
  app.use(path, rpcServer.createMiddleware());

  console.log(`📡 JSON RPC endpoint mounted at ${path}`);
  
  return rpcServer;
}
