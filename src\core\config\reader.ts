/**
 * Configuration reader utilities
 */

import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import { AppConfig } from './types';

export class ConfigReader {
  private configPath: string;
  private environmentOverrides: Record<string, any> = {};

  constructor(configPath?: string) {
    this.configPath = configPath || join(process.cwd(), 'app.conf');
    this.loadEnvironmentOverrides();
  }

  read(): AppConfig {
    const baseConfig = this.readConfigFile();
    const envConfig = this.applyEnvironmentOverrides(baseConfig);
    return this.applyDefaults(envConfig);
  }

  private readConfigFile(): Partial<AppConfig> {
    if (!existsSync(this.configPath)) {
      console.warn(`Configuration file not found at ${this.configPath}, using defaults`);
      return {};
    }

    try {
      const content = readFileSync(this.configPath, 'utf-8');
      return this.parseConfig(content);
    } catch (error) {
      throw new Error(`Failed to read configuration file: ${error}`);
    }
  }

  private parseConfig(content: string): Partial<AppConfig> {
    // Support both JSON and custom format
    content = content.trim();
    
    if (content.startsWith('{')) {
      // JSON format
      return JSON.parse(content);
    }

    // Custom .conf format
    return this.parseConfFormat(content);
  }

  private parseConfFormat(content: string): Partial<AppConfig> {
    const config: any = {};
    const lines = content.split('\n');
    let currentSection: any = config;
    let sectionPath: string[] = [];

    for (const line of lines) {
      const trimmed = line.trim();
      
      // Skip comments and empty lines
      if (!trimmed || trimmed.startsWith('#') || trimmed.startsWith('//')) {
        continue;
      }

      // Section headers [section.subsection]
      if (trimmed.startsWith('[') && trimmed.endsWith(']')) {
        const section = trimmed.slice(1, -1);
        sectionPath = section.split('.');
        currentSection = this.getOrCreateSection(config, sectionPath);
        continue;
      }

      // Key-value pairs
      const equalIndex = trimmed.indexOf('=');
      if (equalIndex > 0) {
        const key = trimmed.slice(0, equalIndex).trim();
        const value = trimmed.slice(equalIndex + 1).trim();
        currentSection[key] = this.parseValue(value);
      }
    }

    return config;
  }

  private getOrCreateSection(config: any, path: string[]): any {
    let current = config;
    for (const segment of path) {
      if (!current[segment]) {
        current[segment] = {};
      }
      current = current[segment];
    }
    return current;
  }

  private parseValue(value: string): any {
    // Remove quotes
    if ((value.startsWith('"') && value.endsWith('"')) ||
        (value.startsWith("'") && value.endsWith("'"))) {
      return value.slice(1, -1);
    }

    // Boolean values
    if (value === 'true') return true;
    if (value === 'false') return false;

    // Numbers
    if (/^\d+$/.test(value)) return parseInt(value, 10);
    if (/^\d+\.\d+$/.test(value)) return parseFloat(value);

    // Arrays (comma-separated)
    if (value.includes(',')) {
      return value.split(',').map(v => this.parseValue(v.trim()));
    }

    return value;
  }

  private loadEnvironmentOverrides(): void {
    // Load environment variables with ERP_ prefix
    for (const [key, value] of Object.entries(process.env)) {
      if (key.startsWith('ERP_')) {
        const configKey = key.slice(4).toLowerCase().replace(/_/g, '.');
        this.environmentOverrides[configKey] = value;
      }
    }
  }

  private applyEnvironmentOverrides(config: Partial<AppConfig>): Partial<AppConfig> {
    const result = { ...config };
    
    for (const [key, value] of Object.entries(this.environmentOverrides)) {
      this.setNestedValue(result, key, value);
    }

    return result;
  }

  private setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    let current = obj;
    
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = this.parseValue(value);
  }

  private applyDefaults(config: Partial<AppConfig>): AppConfig {
    return {
      environment: config.environment || 'development',
      database: {
        host: 'localhost',
        port: 5432,
        database: 'erp_db',
        username: 'postgres',
        password: 'password',
        ssl: false,
        poolSize: 10,
        connectionTimeout: 30000,
        idleTimeout: 30000,
        ...config.database,
      },
      server: {
        host: '0.0.0.0',
        port: 3000,
        cors: {
          enabled: true,
          origins: ['*'],
          credentials: true,
        },
        compression: true,
        helmet: true,
        ...config.server,
      },
      logging: {
        level: 'info',
        format: 'text',
        console: {
          enabled: true,
          colorize: true,
        },
        ...config.logging,
      },
      security: {
        jwtSecret: 'change-me-in-production',
        encryptionKey: 'change-me-in-production',
        sessionTimeout: 3600000, // 1 hour
        passwordPolicy: {
          minLength: 8,
          requireUppercase: true,
          requireLowercase: true,
          requireNumbers: true,
          requireSpecialChars: false,
        },
        ...config.security,
      },
      addons: {
        enabled: true,
        autoLoad: true,
        paths: ['./src/addons'],
        ...config.addons,
      },
      ...config,
    };
  }
}
