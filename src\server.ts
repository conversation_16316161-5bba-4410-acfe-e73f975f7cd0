/**
 * Express Server with JSON RPC 2.0 Support
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import { ConfigReader } from './core/config/reader';
import { ContextManager } from './core/context/manager';
import { setupRpcEndpoint } from './core/rpc/setup';

export class ERPServer {
  private app: express.Application;
  private configReader: ConfigReader;
  private contextManager: ContextManager;
  private server?: any;

  constructor() {
    this.app = express();
    this.configReader = new ConfigReader();
    this.contextManager = new ContextManager();
    
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    const config = this.configReader.getConfig();

    // Security middleware
    this.app.use(helmet());

    // CORS configuration
    if (config.server.cors.enabled) {
      this.app.use(cors({
        origin: config.server.cors.origins,
        credentials: config.server.cors.credentials,
      }));
    }

    // Compression
    if (config.server.compression) {
      this.app.use(compression());
    }

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Request context middleware
    this.app.use(this.contextManager.createMiddleware());

    // Request logging
    this.app.use((req, res, next) => {
      const start = Date.now();
      
      res.on('finish', () => {
        const duration = Date.now() - start;
        const context = this.contextManager.getRequestContext();
        
        console.log(`${req.method} ${req.path} ${res.statusCode} - ${duration}ms`, {
          requestId: context?.requestId,
          correlationId: context?.correlationId,
          userAgent: req.get('User-Agent'),
          ip: req.ip,
        });
      });
      
      next();
    });
  }

  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        status: 'ok',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: '1.0.0',
      });
    });

    // Setup JSON RPC endpoint
    const rpcServer = setupRpcEndpoint(this.app, '/rpc');

    // API documentation endpoint
    this.app.get('/rpc/docs', (req, res) => {
      const methods = rpcServer.listMethods();
      const methodsInfo = methods.map(methodName => {
        const info = rpcServer.getMethodInfo ? rpcServer.getMethodInfo(methodName) : null;
        return {
          name: methodName,
          description: info?.description || 'No description available',
          auth: info?.auth || false,
          permissions: info?.permissions || [],
        };
      });

      res.json({
        title: 'ERP JSON RPC API Documentation',
        version: '2.0',
        methods: methodsInfo,
        endpoints: {
          rpc: '/rpc',
          health: '/health',
          metrics: '/rpc/metrics',
        },
        examples: {
          ping: {
            method: 'POST',
            url: '/rpc',
            body: {
              jsonrpc: '2.0',
              method: 'system.ping',
              id: 1,
            },
          },
          introspection: {
            method: 'POST',
            url: '/rpc',
            body: {
              jsonrpc: '2.0',
              method: 'rpc.listMethods',
              id: 1,
            },
          },
        },
      });
    });

    // Metrics endpoint
    this.app.get('/rpc/metrics', (req, res) => {
      const metrics = rpcServer.getMetrics();
      res.json(metrics);
    });

    // 404 handler
    this.app.use('*', (req, res) => {
      res.status(404).json({
        error: 'Not Found',
        message: `Route ${req.method} ${req.originalUrl} not found`,
        availableEndpoints: [
          'GET /health',
          'GET /rpc/docs',
          'GET /rpc/metrics',
          'POST /rpc',
        ],
      });
    });

    // Error handler
    this.app.use((error: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
      const context = this.contextManager.getRequestContext();
      
      console.error('🚨 Server Error:', {
        error: error.message,
        stack: error.stack,
        requestId: context?.requestId,
        correlationId: context?.correlationId,
        path: req.path,
        method: req.method,
      });

      res.status(500).json({
        error: 'Internal Server Error',
        message: 'An unexpected error occurred',
        requestId: context?.requestId,
      });
    });
  }

  public async start(): Promise<void> {
    const config = this.configReader.getConfig();
    const { host, port } = config.server;

    return new Promise((resolve, reject) => {
      this.server = this.app.listen(port, host, () => {
        console.log(`🚀 ERP Server started successfully!`);
        console.log(`📡 Server running at http://${host}:${port}`);
        console.log(`🔗 JSON RPC endpoint: http://${host}:${port}/rpc`);
        console.log(`📚 API documentation: http://${host}:${port}/rpc/docs`);
        console.log(`📊 Metrics: http://${host}:${port}/rpc/metrics`);
        console.log(`❤️  Health check: http://${host}:${port}/health`);
        resolve();
      });

      this.server.on('error', (error: any) => {
        if (error.code === 'EADDRINUSE') {
          console.error(`❌ Port ${port} is already in use`);
        } else {
          console.error('❌ Server error:', error);
        }
        reject(error);
      });
    });
  }

  public async stop(): Promise<void> {
    if (this.server) {
      return new Promise((resolve) => {
        this.server.close(() => {
          console.log('🛑 Server stopped');
          resolve();
        });
      });
    }
  }

  public getApp(): express.Application {
    return this.app;
  }
}

// Create and export server instance
export const server = new ERPServer();

// Start server if this file is run directly
if (require.main === module) {
  server.start().catch((error) => {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  });

  // Graceful shutdown
  process.on('SIGTERM', async () => {
    console.log('📡 SIGTERM received, shutting down gracefully...');
    await server.stop();
    process.exit(0);
  });

  process.on('SIGINT', async () => {
    console.log('📡 SIGINT received, shutting down gracefully...');
    await server.stop();
    process.exit(0);
  });
}
