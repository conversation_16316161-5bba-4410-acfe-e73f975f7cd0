/**
 * PostgreSQL schema synchronization system
 * Creates and updates tables without dropping them
 */

import { PoolClient } from 'pg';
import { DatabaseConnection } from './connection';

export interface ColumnDefinition {
  name: string;
  type: string;
  nullable?: boolean;
  defaultValue?: string;
  primaryKey?: boolean;
  unique?: boolean;
  references?: {
    table: string;
    column: string;
    onDelete?: 'CASCADE' | 'SET NULL' | 'RESTRICT';
    onUpdate?: 'CASCADE' | 'SET NULL' | 'RESTRICT';
  };
}

export interface IndexDefinition {
  name: string;
  columns: string[];
  unique?: boolean;
  type?: 'btree' | 'hash' | 'gin' | 'gist';
}

export interface TableDefinition {
  name: string;
  columns: ColumnDefinition[];
  indexes?: IndexDefinition[];
  constraints?: string[];
}

export interface SchemaDefinition {
  tables: TableDefinition[];
  version: string;
}

export class SchemaSync {
  private db: DatabaseConnection;

  constructor(db: DatabaseConnection) {
    this.db = db;
  }

  async syncSchema(schema: SchemaDefinition): Promise<void> {
    console.log(`🔄 Starting schema sync for version ${schema.version}`);
    
    await this.db.transaction(async (client) => {
      // Ensure migration tracking table exists
      await this.ensureMigrationTable(client);
      
      // Check if this version is already applied
      const isApplied = await this.isVersionApplied(client, schema.version);
      if (isApplied) {
        console.log(`✅ Schema version ${schema.version} already applied`);
        return;
      }

      // Sync each table
      for (const table of schema.tables) {
        await this.syncTable(client, table);
      }

      // Record migration
      await this.recordMigration(client, schema.version);
      console.log(`✅ Schema sync completed for version ${schema.version}`);
    });
  }

  private async ensureMigrationTable(client: PoolClient): Promise<void> {
    const createMigrationTable = `
      CREATE TABLE IF NOT EXISTS _schema_migrations (
        id SERIAL PRIMARY KEY,
        version VARCHAR(255) NOT NULL UNIQUE,
        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        checksum VARCHAR(64)
      )
    `;
    await client.query(createMigrationTable);
  }

  private async isVersionApplied(client: PoolClient, version: string): Promise<boolean> {
    const result = await client.query(
      'SELECT 1 FROM _schema_migrations WHERE version = $1',
      [version]
    );
    return result.rows.length > 0;
  }

  private async recordMigration(client: PoolClient, version: string): Promise<void> {
    await client.query(
      'INSERT INTO _schema_migrations (version) VALUES ($1)',
      [version]
    );
  }

  private async syncTable(client: PoolClient, table: TableDefinition): Promise<void> {
    const tableExists = await this.tableExists(client, table.name);
    
    if (!tableExists) {
      await this.createTable(client, table);
    } else {
      await this.updateTable(client, table);
    }
  }

  private async tableExists(client: PoolClient, tableName: string): Promise<boolean> {
    const result = await client.query(
      `SELECT 1 FROM information_schema.tables 
       WHERE table_schema = 'public' AND table_name = $1`,
      [tableName]
    );
    return result.rows.length > 0;
  }

  private async createTable(client: PoolClient, table: TableDefinition): Promise<void> {
    console.log(`📝 Creating table: ${table.name}`);
    
    const columnDefs = table.columns.map(col => this.buildColumnDefinition(col));
    const primaryKeys = table.columns.filter(col => col.primaryKey).map(col => col.name);
    
    let sql = `CREATE TABLE ${table.name} (\n  ${columnDefs.join(',\n  ')}`;
    
    if (primaryKeys.length > 0) {
      sql += `,\n  PRIMARY KEY (${primaryKeys.join(', ')})`;
    }
    
    // Add foreign key constraints
    const foreignKeys = table.columns
      .filter(col => col.references)
      .map(col => this.buildForeignKeyConstraint(col));
    
    if (foreignKeys.length > 0) {
      sql += ',\n  ' + foreignKeys.join(',\n  ');
    }
    
    sql += '\n)';
    
    await client.query(sql);
    
    // Create indexes
    if (table.indexes) {
      for (const index of table.indexes) {
        await this.createIndex(client, table.name, index);
      }
    }
  }

  private async updateTable(client: PoolClient, table: TableDefinition): Promise<void> {
    console.log(`🔄 Updating table: ${table.name}`);
    
    const existingColumns = await this.getExistingColumns(client, table.name);
    
    // Add new columns
    for (const column of table.columns) {
      if (!existingColumns.includes(column.name)) {
        await this.addColumn(client, table.name, column);
      }
    }
    
    // Update indexes
    if (table.indexes) {
      for (const index of table.indexes) {
        const indexExists = await this.indexExists(client, index.name);
        if (!indexExists) {
          await this.createIndex(client, table.name, index);
        }
      }
    }
  }

  private async getExistingColumns(client: PoolClient, tableName: string): Promise<string[]> {
    const result = await client.query(
      `SELECT column_name FROM information_schema.columns 
       WHERE table_schema = 'public' AND table_name = $1`,
      [tableName]
    );
    return result.rows.map(row => row.column_name);
  }

  private async addColumn(client: PoolClient, tableName: string, column: ColumnDefinition): Promise<void> {
    console.log(`➕ Adding column: ${tableName}.${column.name}`);
    
    const columnDef = this.buildColumnDefinition(column);
    const sql = `ALTER TABLE ${tableName} ADD COLUMN ${columnDef}`;
    
    await client.query(sql);
    
    // Add foreign key constraint if needed
    if (column.references) {
      const constraintName = `fk_${tableName}_${column.name}`;
      const fkSql = `ALTER TABLE ${tableName} ADD CONSTRAINT ${constraintName} ${this.buildForeignKeyConstraint(column)}`;
      await client.query(fkSql);
    }
  }

  private async indexExists(client: PoolClient, indexName: string): Promise<boolean> {
    const result = await client.query(
      `SELECT 1 FROM pg_indexes WHERE indexname = $1`,
      [indexName]
    );
    return result.rows.length > 0;
  }

  private async createIndex(client: PoolClient, tableName: string, index: IndexDefinition): Promise<void> {
    console.log(`📊 Creating index: ${index.name}`);
    
    const uniqueKeyword = index.unique ? 'UNIQUE ' : '';
    const typeKeyword = index.type ? ` USING ${index.type}` : '';
    const columns = index.columns.join(', ');
    
    const sql = `CREATE ${uniqueKeyword}INDEX ${index.name} ON ${tableName}${typeKeyword} (${columns})`;
    await client.query(sql);
  }

  private buildColumnDefinition(column: ColumnDefinition): string {
    let def = `${column.name} ${column.type}`;
    
    if (!column.nullable) {
      def += ' NOT NULL';
    }
    
    if (column.defaultValue) {
      def += ` DEFAULT ${column.defaultValue}`;
    }
    
    if (column.unique) {
      def += ' UNIQUE';
    }
    
    return def;
  }

  private buildForeignKeyConstraint(column: ColumnDefinition): string {
    if (!column.references) {
      throw new Error(`Column ${column.name} has no foreign key reference`);
    }
    
    const ref = column.references;
    let constraint = `FOREIGN KEY (${column.name}) REFERENCES ${ref.table}(${ref.column})`;
    
    if (ref.onDelete) {
      constraint += ` ON DELETE ${ref.onDelete}`;
    }
    
    if (ref.onUpdate) {
      constraint += ` ON UPDATE ${ref.onUpdate}`;
    }
    
    return constraint;
  }
}
