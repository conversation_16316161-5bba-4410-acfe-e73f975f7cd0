/**
 * Context manager for handling request context lifecycle
 */

import { Request, Response, NextFunction } from 'express';
import { ContextStorage, contextStorage } from './storage';
import { ContextStore, RequestContext, ContextManager as IContextManager } from './types';

export class ContextManager implements IContextManager {
  private storage: ContextStorage;

  constructor(storage?: ContextStorage) {
    this.storage = storage || contextStorage;
  }

  run<T>(context: ContextStore, callback: () => T): T {
    return this.storage.run(context, callback);
  }

  getStore(): ContextStore | undefined {
    return this.storage.getStore();
  }

  getContext(): RequestContext | undefined {
    return this.storage.getContext();
  }

  setContext(context: Partial<RequestContext>): void {
    this.storage.setContext(context);
  }

  updateContext(updates: Partial<RequestContext>): void {
    this.storage.updateContext(updates);
  }

  // Express middleware factory
  createMiddleware() {
    return (req: Request, res: Response, next: NextFunction): void => {
      const context: RequestContext = this.storage.createRequestContext({
        userAgent: req.get('User-Agent'),
        ipAddress: this.getClientIP(req),
        correlationId: req.get('X-Correlation-ID') || undefined,
      });

      const store: ContextStore = { request: context };

      this.storage.run(store, () => {
        // Add correlation ID to response headers
        res.set('X-Correlation-ID', context.correlationId);
        res.set('X-Request-ID', context.requestId);

        // Log request start
        console.log(`🔄 Request started: ${req.method} ${req.path}`, {
          requestId: context.requestId,
          correlationId: context.correlationId,
          userAgent: context.userAgent,
          ip: context.ipAddress,
        });

        // Override res.end to log request completion
        const originalEnd = res.end;
        res.end = function(this: Response, ...args: any[]) {
          const duration = Date.now() - context.startTime.getTime();
          console.log(`✅ Request completed: ${req.method} ${req.path}`, {
            requestId: context.requestId,
            correlationId: context.correlationId,
            statusCode: res.statusCode,
            duration: `${duration}ms`,
          });
          return originalEnd.apply(this, args);
        };

        next();
      });
    };
  }

  // Utility methods for working with context in different scenarios
  async runWithContext<T>(
    contextData: Partial<RequestContext>,
    callback: () => Promise<T>
  ): Promise<T> {
    const context = this.storage.createRequestContext(contextData);
    const store: ContextStore = { request: context };
    
    return new Promise((resolve, reject) => {
      this.storage.run(store, async () => {
        try {
          const result = await callback();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  // Background task context
  createBackgroundContext(taskName: string, metadata: Record<string, any> = {}): RequestContext {
    return this.storage.createRequestContext({
      correlationId: `bg-${Date.now()}`,
      metadata: {
        taskName,
        isBackground: true,
        ...metadata,
      },
    });
  }

  // Database operation context
  createDbContext(operation: string, table?: string): RequestContext {
    const currentContext = this.getContext();
    return this.storage.createRequestContext({
      correlationId: currentContext?.correlationId,
      userId: currentContext?.userId,
      metadata: {
        operation,
        table,
        isDbOperation: true,
        parentRequestId: currentContext?.requestId,
      },
    });
  }

  private getClientIP(req: Request): string {
    return (
      req.get('X-Forwarded-For')?.split(',')[0]?.trim() ||
      req.get('X-Real-IP') ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      'unknown'
    );
  }
}

// Export singleton instance
export const contextManager = new ContextManager();
