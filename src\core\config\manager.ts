/**
 * Configuration manager - main interface for configuration operations
 */

import { watchFile, unwatchFile } from 'fs';
import { AppConfig, ConfigWatcher } from './types';
import { ConfigReader } from './reader';
import { ConfigWriter } from './writer';
import { ConfigValidator } from './validator';

export class ConfigManager implements ConfigWatcher {
  private static instance: ConfigManager;
  private config: AppConfig | null = null;
  private reader: ConfigReader;
  private writer: ConfigWriter;
  private validator: ConfigValidator;
  private watchers: Array<(config: AppConfig) => void> = [];
  private isWatching = false;

  private constructor(configPath?: string) {
    this.reader = new ConfigReader(configPath);
    this.writer = new ConfigWriter(configPath);
    this.validator = new ConfigValidator();
  }

  static getInstance(configPath?: string): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager(configPath);
    }
    return ConfigManager.instance;
  }

  getConfig(): AppConfig {
    if (!this.config) {
      this.loadConfig();
    }
    return this.config!;
  }

  loadConfig(): void {
    const config = this.reader.read();
    const validation = this.validator.validate(config);
    
    if (!validation.valid) {
      const errorMessages = validation.errors.map(e => `${e.path}: ${e.message}`);
      throw new Error(`Configuration validation failed:\n${errorMessages.join('\n')}`);
    }

    this.config = config;
    this.notifyWatchers();
  }

  updateConfig(updates: Partial<AppConfig>): void {
    if (!this.config) {
      this.loadConfig();
    }

    // Merge updates with current config
    const newConfig = this.deepMerge(this.config!, updates);
    
    // Validate the new configuration
    const validation = this.validator.validate(newConfig);
    if (!validation.valid) {
      const errorMessages = validation.errors.map(e => `${e.path}: ${e.message}`);
      throw new Error(`Configuration update validation failed:\n${errorMessages.join('\n')}`);
    }

    // Update in-memory config
    this.config = newConfig;
    
    // Write to file
    this.writer.write(newConfig);
    
    // Notify watchers
    this.notifyWatchers();
  }

  reloadConfig(): void {
    this.loadConfig();
  }

  // ConfigWatcher implementation
  start(): void {
    if (this.isWatching) return;
    
    const configPath = (this.reader as any).configPath;
    watchFile(configPath, () => {
      try {
        this.reloadConfig();
      } catch (error) {
        console.error('Failed to reload configuration:', error);
      }
    });
    
    this.isWatching = true;
  }

  stop(): void {
    if (!this.isWatching) return;
    
    const configPath = (this.reader as any).configPath;
    unwatchFile(configPath);
    this.isWatching = false;
  }

  onChange(callback: (config: AppConfig) => void): void {
    this.watchers.push(callback);
  }

  // Utility methods
  get<T>(path: string): T | undefined {
    return this.getNestedValue(this.getConfig(), path);
  }

  set(path: string, value: any): void {
    const updates = this.createNestedUpdate(path, value);
    this.updateConfig(updates);
  }

  private notifyWatchers(): void {
    if (this.config) {
      this.watchers.forEach(watcher => {
        try {
          watcher(this.config!);
        } catch (error) {
          console.error('Configuration watcher error:', error);
        }
      });
    }
  }

  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  }

  private createNestedUpdate(path: string, value: any): any {
    const keys = path.split('.');
    const result: any = {};
    let current = result;
    
    for (let i = 0; i < keys.length - 1; i++) {
      current[keys[i]] = {};
      current = current[keys[i]];
    }
    
    current[keys[keys.length - 1]] = value;
    return result;
  }
}

// Export singleton instance
export const configManager = ConfigManager.getInstance();
