/**
 * Configuration validation utilities
 */

import { AppConfig, ConfigValidationError, ConfigValidationResult } from './types';

export class ConfigValidator {
  private errors: ConfigValidationError[] = [];

  validate(config: any): ConfigValidationResult {
    this.errors = [];
    
    this.validateEnvironment(config.environment);
    this.validateDatabase(config.database);
    this.validateServer(config.server);
    this.validateLogging(config.logging);
    this.validateSecurity(config.security);
    this.validateAddons(config.addons);

    return {
      valid: this.errors.length === 0,
      errors: this.errors,
    };
  }

  private addError(path: string, message: string, value?: any): void {
    this.errors.push({ path, message, value });
  }

  private validateEnvironment(env: any): void {
    if (!env) {
      this.addError('environment', 'Environment is required');
      return;
    }
    
    const validEnvs = ['development', 'production', 'test'];
    if (!validEnvs.includes(env)) {
      this.addError('environment', `Environment must be one of: ${validEnvs.join(', ')}`, env);
    }
  }

  private validateDatabase(db: any): void {
    if (!db) {
      this.addError('database', 'Database configuration is required');
      return;
    }

    if (!db.host) this.addError('database.host', 'Database host is required');
    if (!db.port || typeof db.port !== 'number') {
      this.addError('database.port', 'Database port must be a number');
    }
    if (!db.database) this.addError('database.database', 'Database name is required');
    if (!db.username) this.addError('database.username', 'Database username is required');
    if (!db.password) this.addError('database.password', 'Database password is required');
  }

  private validateServer(server: any): void {
    if (!server) {
      this.addError('server', 'Server configuration is required');
      return;
    }

    if (!server.host) this.addError('server.host', 'Server host is required');
    if (!server.port || typeof server.port !== 'number') {
      this.addError('server.port', 'Server port must be a number');
    }

    if (server.cors && typeof server.cors !== 'object') {
      this.addError('server.cors', 'CORS configuration must be an object');
    }
  }

  private validateLogging(logging: any): void {
    if (!logging) {
      this.addError('logging', 'Logging configuration is required');
      return;
    }

    const validLevels = ['error', 'warn', 'info', 'debug'];
    if (!validLevels.includes(logging.level)) {
      this.addError('logging.level', `Log level must be one of: ${validLevels.join(', ')}`);
    }

    const validFormats = ['json', 'text'];
    if (!validFormats.includes(logging.format)) {
      this.addError('logging.format', `Log format must be one of: ${validFormats.join(', ')}`);
    }
  }

  private validateSecurity(security: any): void {
    if (!security) {
      this.addError('security', 'Security configuration is required');
      return;
    }

    if (!security.jwtSecret) {
      this.addError('security.jwtSecret', 'JWT secret is required');
    }
    if (!security.encryptionKey) {
      this.addError('security.encryptionKey', 'Encryption key is required');
    }
  }

  private validateAddons(addons: any): void {
    if (!addons) {
      this.addError('addons', 'Addons configuration is required');
      return;
    }

    if (typeof addons.enabled !== 'boolean') {
      this.addError('addons.enabled', 'Addons enabled must be a boolean');
    }
    if (typeof addons.autoLoad !== 'boolean') {
      this.addError('addons.autoLoad', 'Addons autoLoad must be a boolean');
    }
    if (!Array.isArray(addons.paths)) {
      this.addError('addons.paths', 'Addons paths must be an array');
    }
  }
}
