"use strict";

/**
 * Module dependencies.
 */

const util = require('util');
const Stream = require('stream');
const ResponseBase = require('../response-base');
const {
  mixin
} = require('../utils');

/**
 * Expose `Response`.
 */

module.exports = Response;

/**
 * Initialize a new `Response` with the given `xhr`.
 *
 *  - set flags (.ok, .error, etc)
 *  - parse header
 *
 * @param {Request} req
 * @param {Object} options
 * @constructor
 * @extends {Stream}
 * @implements {ReadableStream}
 * @api private
 */

function Response(request) {
  Stream.call(this);
  this.res = request.res;
  const {
    res
  } = this;
  this.request = request;
  this.req = request.req;
  this.text = res.text;
  this.files = res.files || {};
  this.buffered = request._resBuffered;
  this.headers = res.headers;
  this.header = this.headers;
  this._setStatusProperties(res.statusCode);
  this._setHeaderProperties(this.header);
  this.setEncoding = res.setEncoding.bind(res);
  res.on('data', this.emit.bind(this, 'data'));
  res.on('end', this.emit.bind(this, 'end'));
  res.on('close', this.emit.bind(this, 'close'));
  res.on('error', this.emit.bind(this, 'error'));
}

// Lazy access res.body.
// https://github.com/nodejs/node/pull/39520#issuecomment-889697136
Object.defineProperty(Response.prototype, 'body', {
  get() {
    return this._body !== undefined ? this._body : this.res.body !== undefined ? this.res.body : {};
  },
  set(value) {
    this._body = value;
  }
});

/**
 * Inherit from `Stream`.
 */

util.inherits(Response, Stream);
mixin(Response.prototype, ResponseBase.prototype);

/**
 * Implements methods of a `ReadableStream`
 */

Response.prototype.destroy = function (error) {
  this.res.destroy(error);
};

/**
 * Pause.
 */

Response.prototype.pause = function () {
  this.res.pause();
};

/**
 * Resume.
 */

Response.prototype.resume = function () {
  this.res.resume();
};

/**
 * Return an `Error` representative of this response.
 *
 * @return {Error}
 * @api public
 */

Response.prototype.toError = function () {
  const {
    req
  } = this;
  const {
    method
  } = req;
  const {
    path
  } = req;
  const message = `cannot ${method} ${path} (${this.status})`;
  const error = new Error(message);
  error.status = this.status;
  error.text = this.text;
  error.method = method;
  error.path = path;
  return error;
};
Response.prototype.setStatusProperties = function (status) {
  console.warn('In superagent 2.x setStatusProperties is a private method');
  return this._setStatusProperties(status);
};

/**
 * To json.
 *
 * @return {Object}
 * @api public
 */

Response.prototype.toJSON = function () {
  return {
    req: this.request.toJSON(),
    header: this.header,
    status: this.status,
    text: this.text
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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