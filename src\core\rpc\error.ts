/**
 * JSON RPC 2.0 error handling
 */

import { JsonRpcError, JsonRpcErrorCode } from './types';

export class RpcError extends Error {
  public code: number;
  public data?: any;

  constructor(code: number, message: string, data?: any) {
    super(message);
    this.name = 'RpcError';
    this.code = code;
    this.data = data;
  }

  toJsonRpcError(): JsonRpcError {
    return {
      code: this.code,
      message: this.message,
      data: this.data,
    };
  }
}

export class ParseError extends RpcError {
  constructor(message = 'Parse error') {
    super(JsonRpcErrorCode.PARSE_ERROR, message);
  }
}

export class InvalidRequestError extends RpcError {
  constructor(message = 'Invalid Request') {
    super(JsonRpcErrorCode.INVALID_REQUEST, message);
  }
}

export class MethodNotFoundError extends RpcError {
  constructor(method: string) {
    super(JsonRpcErrorCode.METHOD_NOT_FOUND, `Method not found: ${method}`);
  }
}

export class InvalidParamsError extends RpcError {
  constructor(message = 'Invalid params', data?: any) {
    super(JsonRpcErrorCode.INVALID_PARAMS, message, data);
  }
}

export class InternalError extends RpcError {
  constructor(message = 'Internal error', data?: any) {
    super(JsonRpcErrorCode.INTERNAL_ERROR, message, data);
  }
}

export class AuthenticationError extends RpcError {
  constructor(message = 'Authentication required') {
    super(JsonRpcErrorCode.AUTHENTICATION_ERROR, message);
  }
}

export class AuthorizationError extends RpcError {
  constructor(message = 'Insufficient permissions') {
    super(JsonRpcErrorCode.AUTHORIZATION_ERROR, message);
  }
}

export class ValidationError extends RpcError {
  constructor(message = 'Validation failed', data?: any) {
    super(JsonRpcErrorCode.VALIDATION_ERROR, message, data);
  }
}

export class BusinessLogicError extends RpcError {
  constructor(message: string, data?: any) {
    super(JsonRpcErrorCode.BUSINESS_LOGIC_ERROR, message, data);
  }
}

export class DatabaseError extends RpcError {
  constructor(message = 'Database operation failed', data?: any) {
    super(JsonRpcErrorCode.DATABASE_ERROR, message, data);
  }
}

export function createErrorResponse(
  error: Error | RpcError,
  id: string | number | null
): { jsonrpc: '2.0'; error: JsonRpcError; id: string | number | null } {
  let jsonRpcError: JsonRpcError;

  if (error instanceof RpcError) {
    jsonRpcError = error.toJsonRpcError();
  } else {
    // Convert generic errors to internal errors
    jsonRpcError = {
      code: JsonRpcErrorCode.INTERNAL_ERROR,
      message: error.message || 'Internal error',
    };
  }

  return {
    jsonrpc: '2.0',
    error: jsonRpcError,
    id,
  };
}
