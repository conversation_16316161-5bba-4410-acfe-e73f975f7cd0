# Addons Directory

This directory contains modular addons/plugins for the ERP system.

## Structure

Each addon should follow this structure:

```
addons/
├── addon-name/
│   ├── index.ts          # Main addon entry point
│   ├── package.json      # Addon metadata
│   ├── config/           # Addon-specific configuration
│   ├── models/           # Addon data models
│   ├── services/         # Addon business logic
│   ├── rpc/              # Addon RPC endpoints
│   └── tests/            # Addon tests
```

## Creating an Addon

1. Create a new directory with your addon name
2. Implement the `AddonInterface` from `@/core/addon`
3. Export your addon from `index.ts`
4. Add addon metadata in `package.json`

## Example Addon

See the `example-addon` directory for a complete implementation example.
