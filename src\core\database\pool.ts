/**
 * Database pool management and health monitoring
 */

import { Pool, PoolClient } from 'pg';
import { DatabaseConnection } from './connection';

export interface PoolStats {
  totalConnections: number;
  idleConnections: number;
  waitingClients: number;
}

export class DatabasePool {
  private connection: DatabaseConnection;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private isHealthy = true;

  constructor(connection: DatabaseConnection) {
    this.connection = connection;
  }

  async startHealthMonitoring(intervalMs = 30000): Promise<void> {
    if (this.healthCheckInterval) {
      return;
    }

    this.healthCheckInterval = setInterval(async () => {
      const healthy = await this.connection.healthCheck();
      if (healthy !== this.isHealthy) {
        this.isHealthy = healthy;
        console.log(`Database health status changed: ${healthy ? 'HEALTHY' : 'UNHEALTHY'}`);
      }
    }, intervalMs);

    console.log('✅ Database health monitoring started');
  }

  stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log('✅ Database health monitoring stopped');
    }
  }

  getStats(): PoolStats {
    const pool = this.connection.getPool();
    return {
      totalConnections: pool.totalCount,
      idleConnections: pool.idleCount,
      waitingClients: pool.waitingCount,
    };
  }

  isHealthyStatus(): boolean {
    return this.isHealthy;
  }

  async executeWithRetry<T>(
    operation: (client: PoolClient) => Promise<T>,
    maxRetries = 3,
    retryDelay = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const client = await this.connection.getClient();
        try {
          return await operation(client);
        } finally {
          client.release();
        }
      } catch (error) {
        lastError = error as Error;
        console.warn(`Database operation attempt ${attempt} failed:`, error);
        
        if (attempt < maxRetries) {
          await this.delay(retryDelay * attempt);
        }
      }
    }

    throw lastError!;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
