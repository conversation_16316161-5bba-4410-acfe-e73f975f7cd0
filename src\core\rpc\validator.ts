/**
 * JSON RPC 2.0 request validation
 */

import { JsonRpcRequest, JsonRpcNotification } from './types';
import { InvalidRequestError, InvalidParamsError } from './error';

export class RpcValidator {
  validateRequest(data: any): JsonRpcRequest {
    // Check if it's a valid JSON RPC request
    if (!data || typeof data !== 'object') {
      throw new InvalidRequestError('Request must be an object');
    }

    // Check jsonrpc version
    if (data.jsonrpc !== '2.0') {
      throw new InvalidRequestError('Invalid jsonrpc version. Must be "2.0"');
    }

    // Check method
    if (!data.method || typeof data.method !== 'string') {
      throw new InvalidRequestError('Method must be a non-empty string');
    }

    // Method names starting with "rpc." are reserved
    if (data.method.startsWith('rpc.')) {
      throw new InvalidRequestError('Method names starting with "rpc." are reserved');
    }

    // Validate params if present
    if (data.params !== undefined) {
      if (!Array.isArray(data.params) && typeof data.params !== 'object') {
        throw new InvalidRequestError('Params must be an array or object');
      }
    }

    // Validate id if present (can be string, number, or null)
    if (data.id !== undefined && data.id !== null) {
      if (typeof data.id !== 'string' && typeof data.id !== 'number') {
        throw new InvalidRequestError('ID must be a string, number, or null');
      }
    }

    return data as JsonRpcRequest;
  }

  validateNotification(data: any): JsonRpcNotification {
    const request = this.validateRequest(data);
    
    // Notifications must not have an id
    if ('id' in data) {
      throw new InvalidRequestError('Notifications must not have an id field');
    }

    return request as JsonRpcNotification;
  }

  validateBatchRequest(data: any): JsonRpcRequest[] {
    if (!Array.isArray(data)) {
      throw new InvalidRequestError('Batch request must be an array');
    }

    if (data.length === 0) {
      throw new InvalidRequestError('Batch request cannot be empty');
    }

    return data.map((item, index) => {
      try {
        return this.validateRequest(item);
      } catch (error) {
        throw new InvalidRequestError(`Invalid request at index ${index}: ${error.message}`);
      }
    });
  }

  validateParams(params: any, schema?: any): void {
    if (!schema) {
      return; // No validation schema provided
    }

    // Basic JSON Schema validation
    // In a production environment, you might want to use a proper JSON Schema validator
    this.validateAgainstSchema(params, schema);
  }

  private validateAgainstSchema(data: any, schema: any): void {
    if (schema.type) {
      this.validateType(data, schema.type);
    }

    if (schema.properties && typeof data === 'object' && data !== null) {
      this.validateObjectProperties(data, schema.properties, schema.required);
    }

    if (schema.items && Array.isArray(data)) {
      this.validateArrayItems(data, schema.items);
    }

    if (schema.enum && !schema.enum.includes(data)) {
      throw new InvalidParamsError(`Value must be one of: ${schema.enum.join(', ')}`);
    }

    if (schema.minimum !== undefined && typeof data === 'number' && data < schema.minimum) {
      throw new InvalidParamsError(`Value must be >= ${schema.minimum}`);
    }

    if (schema.maximum !== undefined && typeof data === 'number' && data > schema.maximum) {
      throw new InvalidParamsError(`Value must be <= ${schema.maximum}`);
    }

    if (schema.minLength !== undefined && typeof data === 'string' && data.length < schema.minLength) {
      throw new InvalidParamsError(`String must be at least ${schema.minLength} characters`);
    }

    if (schema.maxLength !== undefined && typeof data === 'string' && data.length > schema.maxLength) {
      throw new InvalidParamsError(`String must be at most ${schema.maxLength} characters`);
    }
  }

  private validateType(data: any, expectedType: string): void {
    const actualType = Array.isArray(data) ? 'array' : typeof data;
    
    if (actualType !== expectedType) {
      throw new InvalidParamsError(`Expected ${expectedType}, got ${actualType}`);
    }
  }

  private validateObjectProperties(
    data: Record<string, any>,
    properties: Record<string, any>,
    required?: string[]
  ): void {
    // Check required properties
    if (required) {
      for (const prop of required) {
        if (!(prop in data)) {
          throw new InvalidParamsError(`Missing required property: ${prop}`);
        }
      }
    }

    // Validate each property
    for (const [prop, value] of Object.entries(data)) {
      if (properties[prop]) {
        try {
          this.validateAgainstSchema(value, properties[prop]);
        } catch (error) {
          throw new InvalidParamsError(`Invalid property '${prop}': ${error.message}`);
        }
      }
    }
  }

  private validateArrayItems(data: any[], itemSchema: any): void {
    for (let i = 0; i < data.length; i++) {
      try {
        this.validateAgainstSchema(data[i], itemSchema);
      } catch (error) {
        throw new InvalidParamsError(`Invalid array item at index ${i}: ${error.message}`);
      }
    }
  }
}
