/**
 * Configuration system types
 */

export interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  ssl?: boolean;
  poolSize?: number;
  connectionTimeout?: number;
  idleTimeout?: number;
}

export interface ServerConfig {
  host: string;
  port: number;
  cors: {
    enabled: boolean;
    origins: string[];
    credentials: boolean;
  };
  compression: boolean;
  helmet: boolean;
  rateLimit?: {
    windowMs: number;
    max: number;
  };
}

export interface LoggingConfig {
  level: 'error' | 'warn' | 'info' | 'debug';
  format: 'json' | 'text';
  file?: {
    enabled: boolean;
    path: string;
    maxSize: string;
    maxFiles: number;
  };
  console: {
    enabled: boolean;
    colorize: boolean;
  };
}

export interface SecurityConfig {
  jwtSecret: string;
  encryptionKey: string;
  sessionTimeout: number;
  passwordPolicy: {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
  };
}

export interface AddonConfig {
  enabled: boolean;
  autoLoad: boolean;
  paths: string[];
  whitelist?: string[];
  blacklist?: string[];
}

export interface AppConfig {
  environment: 'development' | 'production' | 'test';
  database: DatabaseConfig;
  server: ServerConfig;
  logging: LoggingConfig;
  security: SecurityConfig;
  addons: AddonConfig;
  [key: string]: any; // Allow custom configuration sections
}

export interface ConfigValidationError {
  path: string;
  message: string;
  value?: any;
}

export interface ConfigValidationResult {
  valid: boolean;
  errors: ConfigValidationError[];
}

export interface ConfigWatcher {
  start(): void;
  stop(): void;
  onChange(callback: (config: AppConfig) => void): void;
}
