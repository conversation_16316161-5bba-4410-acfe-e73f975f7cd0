/**
 * JSON RPC 2.0 method handler and registry
 */

import { 
  RpcMethodDefinition, 
  RpcMethod<PERSON>and<PERSON>, 
  RpcContext, 
  RpcMiddleware,
  JsonRpcRequest,
  RpcMetrics
} from './types';
import { MethodNotFoundError, AuthenticationError, AuthorizationError } from './error';
import { RpcValidator } from './validator';

export class RpcMethodRegistry {
  private methods = new Map<string, RpcMethodDefinition>();
  private middleware: RpcMiddleware[] = [];
  private validator = new RpcValidator();
  private metrics: RpcMetrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageResponseTime: 0,
    methodStats: {},
  };

  register(definition: RpcMethodDefinition): void {
    if (this.methods.has(definition.name)) {
      throw new Error(`Method ${definition.name} is already registered`);
    }

    this.methods.set(definition.name, definition);
    console.log(`📝 Registered RPC method: ${definition.name}`);
  }

  unregister(methodName: string): boolean {
    const removed = this.methods.delete(methodName);
    if (removed) {
      console.log(`🗑️ Unregistered RPC method: ${methodName}`);
    }
    return removed;
  }

  addMiddleware(middleware: RpcMiddleware): void {
    this.middleware.push(middleware);
  }

  async execute(request: JsonRpcRequest, context: RpcContext): Promise<any> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Get method definition
      const methodDef = this.methods.get(request.method);
      if (!methodDef) {
        throw new MethodNotFoundError(request.method);
      }

      // Initialize method stats if not exists
      if (!this.metrics.methodStats[request.method]) {
        this.metrics.methodStats[request.method] = {
          calls: 0,
          errors: 0,
          totalTime: 0,
          averageTime: 0,
        };
      }

      const methodStats = this.metrics.methodStats[request.method];
      methodStats.calls++;

      // Check authentication if required
      if (methodDef.auth && !context.userId) {
        throw new AuthenticationError();
      }

      // Check permissions if specified
      if (methodDef.permissions && methodDef.permissions.length > 0) {
        await this.checkPermissions(context, methodDef.permissions);
      }

      // Validate parameters
      if (methodDef.params) {
        this.validator.validateParams(request.params, methodDef.params.schema);
      }

      // Execute middleware chain
      const result = await this.executeWithMiddleware(
        request,
        context,
        methodDef.handler
      );

      // Update metrics
      const duration = Date.now() - startTime;
      this.updateSuccessMetrics(request.method, duration);

      return result;
    } catch (error) {
      // Update error metrics
      const duration = Date.now() - startTime;
      this.updateErrorMetrics(request.method, duration);
      throw error;
    }
  }

  private async executeWithMiddleware(
    request: JsonRpcRequest,
    context: RpcContext,
    handler: RpcMethodHandler
  ): Promise<any> {
    let index = 0;

    const next = async (): Promise<any> => {
      if (index < this.middleware.length) {
        const middleware = this.middleware[index++];
        return middleware(request, context, next);
      } else {
        return handler(request.params, context);
      }
    };

    return next();
  }

  private async checkPermissions(context: RpcContext, requiredPermissions: string[]): Promise<void> {
    // This is a placeholder - implement your permission checking logic
    // You might want to check against a database or external service
    const userPermissions = context.metadata.permissions || [];
    
    const hasPermission = requiredPermissions.every(permission =>
      userPermissions.includes(permission)
    );

    if (!hasPermission) {
      throw new AuthorizationError(`Missing required permissions: ${requiredPermissions.join(', ')}`);
    }
  }

  private updateSuccessMetrics(methodName: string, duration: number): void {
    this.metrics.successfulRequests++;
    
    const methodStats = this.metrics.methodStats[methodName];
    methodStats.totalTime += duration;
    methodStats.averageTime = methodStats.totalTime / methodStats.calls;

    this.updateAverageResponseTime();
  }

  private updateErrorMetrics(methodName: string, duration: number): void {
    this.metrics.failedRequests++;
    
    if (this.metrics.methodStats[methodName]) {
      this.metrics.methodStats[methodName].errors++;
    }

    this.updateAverageResponseTime();
  }

  private updateAverageResponseTime(): void {
    const totalTime = Object.values(this.metrics.methodStats)
      .reduce((sum, stats) => sum + stats.totalTime, 0);
    
    this.metrics.averageResponseTime = this.metrics.totalRequests > 0
      ? totalTime / this.metrics.totalRequests
      : 0;
  }

  // Introspection methods
  listMethods(): string[] {
    return Array.from(this.methods.keys());
  }

  getMethodInfo(methodName: string): RpcMethodDefinition | undefined {
    return this.methods.get(methodName);
  }

  getMetrics(): RpcMetrics {
    return { ...this.metrics };
  }

  resetMetrics(): void {
    this.metrics = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      methodStats: {},
    };
  }
}
