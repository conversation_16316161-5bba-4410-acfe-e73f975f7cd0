/**
 * Context types for async local storage
 */

export interface RequestContext {
  requestId: string;
  correlationId: string;
  userId?: string;
  sessionId?: string;
  userAgent?: string;
  ipAddress?: string;
  startTime: Date;
  metadata: Record<string, any>;
}

export interface ContextStore {
  request?: RequestContext;
  [key: string]: any;
}

export interface ContextManager {
  run<T>(context: ContextStore, callback: () => T): T;
  getStore(): ContextStore | undefined;
  getContext(): RequestContext | undefined;
  setContext(context: Partial<RequestContext>): void;
  updateContext(updates: Partial<RequestContext>): void;
}
