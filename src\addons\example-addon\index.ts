/**
 * Example addon implementation
 */

import { AddonInterface, AddonMetadata } from '@/core/addon';

export class ExampleAddon implements AddonInterface {
  public readonly metadata: AddonMetadata = {
    name: 'example-addon',
    version: '1.0.0',
    description: 'Example addon for demonstration',
    author: 'ERP Team',
    dependencies: [],
  };

  async initialize(): Promise<void> {
    console.log(`Initializing ${this.metadata.name} addon`);
  }

  async start(): Promise<void> {
    console.log(`Starting ${this.metadata.name} addon`);
  }

  async stop(): Promise<void> {
    console.log(`Stopping ${this.metadata.name} addon`);
  }

  async destroy(): Promise<void> {
    console.log(`Destroying ${this.metadata.name} addon`);
  }
}

export default ExampleAddon;
