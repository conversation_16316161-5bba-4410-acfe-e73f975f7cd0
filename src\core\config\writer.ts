/**
 * Configuration writer utilities
 */

import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { dirname } from 'path';
import { AppConfig } from './types';

export class ConfigWriter {
  private configPath: string;

  constructor(configPath?: string) {
    this.configPath = configPath || 'app.conf';
  }

  write(config: AppConfig, format: 'json' | 'conf' = 'conf'): void {
    this.ensureDirectoryExists();
    
    const content = format === 'json' 
      ? this.toJSON(config)
      : this.toConfFormat(config);
    
    try {
      writeFileSync(this.configPath, content, 'utf-8');
    } catch (error) {
      throw new Error(`Failed to write configuration file: ${error}`);
    }
  }

  update(updates: Partial<AppConfig>): void {
    // Read existing config, merge updates, and write back
    const reader = new (require('./reader').ConfigReader)(this.configPath);
    const currentConfig = reader.read();
    const mergedConfig = this.deepMerge(currentConfig, updates);
    this.write(mergedConfig);
  }

  private ensureDirectoryExists(): void {
    const dir = dirname(this.configPath);
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }
  }

  private toJSON(config: AppConfig): string {
    return JSON.stringify(config, null, 2);
  }

  private toConfFormat(config: AppConfig): string {
    let content = '# ERP Application Configuration\n';
    content += `# Generated on ${new Date().toISOString()}\n\n`;
    
    content += this.formatSection('', config, 0);
    return content;
  }

  private formatSection(sectionName: string, obj: any, depth: number): string {
    let content = '';
    
    if (sectionName && depth > 0) {
      content += `[${sectionName}]\n`;
    }

    for (const [key, value] of Object.entries(obj)) {
      if (value && typeof value === 'object' && !Array.isArray(value)) {
        // Nested section
        const nestedSectionName = sectionName 
          ? `${sectionName}.${key}` 
          : key;
        content += '\n' + this.formatSection(nestedSectionName, value, depth + 1);
      } else {
        // Key-value pair
        content += `${key} = ${this.formatValue(value)}\n`;
      }
    }

    return content + '\n';
  }

  private formatValue(value: any): string {
    if (typeof value === 'string') {
      return `"${value}"`;
    }
    if (Array.isArray(value)) {
      return value.map(v => this.formatValue(v)).join(', ');
    }
    return String(value);
  }

  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }
}
