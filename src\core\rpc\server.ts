/**
 * JSON RPC 2.0 Server Implementation
 */

import { Request, Response, NextFunction } from 'express';
import { 
  JsonRpcRequest, 
  JsonRpcResponse, 
  JsonRpcNotification,
  RpcServerOptions,
  RpcContext,
  RpcMethodDefinition,
  RpcMiddleware
} from './types';
import { RpcMethodRegistry } from './handler';
import { RpcValidator } from './validator';
import { 
  RpcError, 
  ParseError, 
  InvalidRequestError, 
  createErrorResponse 
} from './error';
import { ContextManager } from '../context/manager';

export class JsonRpcServer {
  private registry: RpcMethodRegistry;
  private validator: RpcValidator;
  private contextManager: ContextManager;
  private options: Required<RpcServerOptions>;

  constructor(options: RpcServerOptions = {}) {
    this.registry = new RpcMethodRegistry();
    this.validator = new RpcValidator();
    this.contextManager = new ContextManager();
    
    // Set default options
    this.options = {
      cors: options.cors ?? true,
      maxRequestSize: options.maxRequestSize ?? '10mb',
      timeout: options.timeout ?? 30000,
      enableIntrospection: options.enableIntrospection ?? true,
      enableMetrics: options.enableMetrics ?? true,
    };

    // Register built-in introspection methods if enabled
    if (this.options.enableIntrospection) {
      this.registerIntrospectionMethods();
    }
  }

  /**
   * Register a new RPC method
   */
  register(definition: RpcMethodDefinition): void {
    this.registry.register(definition);
  }

  /**
   * Unregister an RPC method
   */
  unregister(methodName: string): boolean {
    return this.registry.unregister(methodName);
  }

  /**
   * Add middleware to the RPC server
   */
  use(middleware: RpcMiddleware): void {
    this.registry.addMiddleware(middleware);
  }

  /**
   * Create Express middleware for handling JSON RPC requests
   */
  createMiddleware() {
    return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
      // Only handle POST requests to RPC endpoints
      if (req.method !== 'POST') {
        return next();
      }

      // Set CORS headers if enabled
      if (this.options.cors) {
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'POST, OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Correlation-ID');
        
        if (req.method === 'OPTIONS') {
          return res.status(200).end();
        }
      }

      try {
        // Parse request body
        const requestData = req.body;
        
        // Create RPC context from request context
        const requestContext = this.contextManager.getRequestContext();
        const rpcContext: RpcContext = {
          requestId: requestContext?.requestId || `rpc-${Date.now()}`,
          correlationId: requestContext?.correlationId || `rpc-${Date.now()}`,
          userId: req.headers['x-user-id'] as string,
          sessionId: req.headers['x-session-id'] as string,
          metadata: {
            userAgent: req.get('User-Agent'),
            ipAddress: req.ip,
            ...requestContext?.metadata,
          },
        };

        // Handle batch or single request
        const response = Array.isArray(requestData)
          ? await this.handleBatchRequest(requestData, rpcContext)
          : await this.handleSingleRequest(requestData, rpcContext);

        // Set response headers
        res.setHeader('Content-Type', 'application/json');
        
        // Send response (only if not a notification)
        if (response !== null) {
          res.json(response);
        } else {
          res.status(204).end(); // No Content for notifications
        }

      } catch (error) {
        console.error('🚨 RPC Server Error:', error);
        
        // Handle parse errors
        if (error instanceof SyntaxError) {
          const parseError = new ParseError('Invalid JSON');
          res.status(400).json(createErrorResponse(parseError, null));
        } else {
          const internalError = createErrorResponse(error as Error, null);
          res.status(500).json(internalError);
        }
      }
    };
  }

  /**
   * Handle a single JSON RPC request
   */
  private async handleSingleRequest(
    data: any, 
    context: RpcContext
  ): Promise<JsonRpcResponse | null> {
    try {
      // Validate request format
      const request = this.validator.validateRequest(data);
      
      // Check if it's a notification (no id field)
      const isNotification = !('id' in data);
      
      // Execute the method
      const result = await this.registry.execute(request, context);
      
      // Return null for notifications (no response)
      if (isNotification) {
        return null;
      }
      
      // Return success response
      return {
        jsonrpc: '2.0',
        result,
        id: request.id!,
      };
      
    } catch (error) {
      // For notifications, don't send error responses
      if (!('id' in data)) {
        console.error('🚨 Notification error (not sent to client):', error);
        return null;
      }
      
      // Return error response
      return createErrorResponse(error as Error, data.id || null);
    }
  }

  /**
   * Handle a batch JSON RPC request
   */
  private async handleBatchRequest(
    data: any[], 
    context: RpcContext
  ): Promise<JsonRpcResponse[]> {
    try {
      // Validate batch request
      const requests = this.validator.validateBatchRequest(data);
      
      // Process all requests in parallel
      const responses = await Promise.all(
        requests.map(async (request, index) => {
          try {
            return await this.handleSingleRequest(data[index], {
              ...context,
              requestId: `${context.requestId}-batch-${index}`,
            });
          } catch (error) {
            // Individual request errors are handled in handleSingleRequest
            return createErrorResponse(error as Error, request.id || null);
          }
        })
      );
      
      // Filter out null responses (notifications)
      return responses.filter((response): response is JsonRpcResponse => response !== null);
      
    } catch (error) {
      // Batch validation error
      throw error;
    }
  }

  /**
   * Register built-in introspection methods
   */
  private registerIntrospectionMethods(): void {
    // List all available methods
    this.register({
      name: 'rpc.listMethods',
      handler: async () => {
        return this.registry.listMethods();
      },
      description: 'List all available RPC methods',
    });

    // Get method information
    this.register({
      name: 'rpc.methodInfo',
      handler: async (params) => {
        if (!params || !params.method) {
          throw new InvalidRequestError('Method name is required');
        }
        
        const info = this.registry.getMethodInfo(params.method);
        if (!info) {
          throw new InvalidRequestError(`Method '${params.method}' not found`);
        }
        
        return {
          name: info.name,
          description: info.description,
          params: info.params,
          returns: info.returns,
          auth: info.auth,
          permissions: info.permissions,
        };
      },
      description: 'Get information about a specific method',
      params: {
        type: 'object',
        schema: {
          type: 'object',
          properties: {
            method: { type: 'string' }
          },
          required: ['method']
        }
      },
    });

    // Get server metrics (if enabled)
    if (this.options.enableMetrics) {
      this.register({
        name: 'rpc.metrics',
        handler: async () => {
          return this.registry.getMetrics();
        },
        description: 'Get server performance metrics',
      });
    }

    // Server info
    this.register({
      name: 'rpc.serverInfo',
      handler: async () => {
        return {
          version: '2.0',
          server: 'ERP-TS JSON RPC Server',
          features: {
            introspection: this.options.enableIntrospection,
            metrics: this.options.enableMetrics,
            batch: true,
            notifications: true,
          },
        };
      },
      description: 'Get server information and capabilities',
    });
  }

  /**
   * Get server metrics
   */
  getMetrics() {
    return this.registry.getMetrics();
  }

  /**
   * Reset server metrics
   */
  resetMetrics(): void {
    this.registry.resetMetrics();
  }

  /**
   * Get list of registered methods
   */
  listMethods(): string[] {
    return this.registry.listMethods();
  }
}
