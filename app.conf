# ERP Application Configuration
# Generated on 2025-07-12T00:00:00.000Z

environment = "development"

[database]
host = "localhost"
port = 5432
database = "erp_db"
username = "postgres"
password = "password"
ssl = false
poolSize = 10
connectionTimeout = 30000
idleTimeout = 30000

[server]
host = "0.0.0.0"
port = 3000
compression = true
helmet = true

[server.cors]
enabled = true
origins = "*"
credentials = true

[logging]
level = "info"
format = "text"

[logging.console]
enabled = true
colorize = true

[security]
jwtSecret = "change-me-in-production"
encryptionKey = "change-me-in-production"
sessionTimeout = 3600000

[security.passwordPolicy]
minLength = 8
requireUppercase = true
requireLowercase = true
requireNumbers = true
requireSpecialChars = false

[addons]
enabled = true
autoLoad = true
paths = "./src/addons"
